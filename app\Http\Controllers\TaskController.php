<?php

namespace App\Http\Controllers;

use App\Models\Task;
use Illuminate\Http\Request;
use Illuminate\Routing\Controllers\HasMiddleware;
use Illuminate\Routing\Controllers\Middleware;
use Illuminate\Support\Facades\Auth;
use Illuminate\Validation\Rule;
use OpenApi\Attributes as OA;

class TaskController extends Controller implements HasMiddleware
{
    public static function middleware(): array
    {
        return [
            new Middleware('auth:user'),
        ];
    }

    /**
     * @OA\Get(
     *     path="/api/tasks",
     *     summary="Get tasks",
     *     description="Get tasks filtered by status and ordered by latest",
     *     tags={"Tasks"},
     *     security={{"sanctum": {}}},
     *     @OA\Parameter(
     *         name="status",
     *         in="query",
     *         description="Filter by status ID",
     *         required=false,
     *         @OA\Schema(type="integer", example=1)
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Tasks retrieved successfully",
     *         @OA\JsonContent(
     *             @OA\Property(property="status", type="boolean", example=true),
     *             @OA\Property(property="message", type="string", example="Tasks retrieved successfully"),
     *             @OA\Property(
     *                 property="data",
     *                 type="array",
     *                 @OA\Items(
     *                     @OA\Property(property="id", type="integer", example=1),
     *                     @OA\Property(property="title", type="string", example="Task Title"),
     *                     @OA\Property(property="description", type="string", example="Task description"),
     *                     @OA\Property(property="report", type="string", nullable=true, example="Task report"),
     *                     @OA\Property(property="creator_id", type="integer", example=1),
     *                     @OA\Property(property="assignee_id", type="integer", example=2),
     *                     @OA\Property(property="status_id", type="integer", example=1),
     *                     @OA\Property(property="created_at", type="string", format="date-time"),
     *                     @OA\Property(property="updated_at", type="string", format="date-time")
     *                 )
     *             )
     *         )
     *     )
     * )
     */
    public function index(Request $request)
    {
        $query = Task::with(['creator', 'assignee', 'status'])
            ->orderBy('created_at', 'desc');

        if ($request->has('status')) {
            $query->where('status_id', $request->status);
        }

        $tasks = $query->get();

        return $this->success($tasks, 'Tasks retrieved successfully');
    }

    /**
     * @OA\Post(
     *     path="/api/tasks",
     *     summary="Create a new task",
     *     description="Create a new task with creator as logged in user and default status 1 (To Do)",
     *     tags={"Tasks"},
     *     security={{"sanctum": {}}},
     *     @OA\RequestBody(
     *         required=true,
     *         @OA\JsonContent(
     *             required={"title", "assignee_id"},
     *             @OA\Property(property="title", type="string", maxLength=150, example="New Task"),
     *             @OA\Property(property="description", type="string", example="Task description"),
     *             @OA\Property(property="assignee_id", type="integer", example=2)
     *         )
     *     ),
     *     @OA\Response(
     *         response=201,
     *         description="Task created successfully",
     *         @OA\JsonContent(
     *             @OA\Property(property="status", type="boolean", example=true),
     *             @OA\Property(property="message", type="string", example="Task created successfully"),
     *             @OA\Property(property="data", type="object")
     *         )
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Validation error",
     *         @OA\JsonContent(
     *             @OA\Property(property="status", type="boolean", example=false),
     *             @OA\Property(property="message", type="string", example="Validation failed")
     *         )
     *     )
     * )
     */
    public function store(Request $request)
    {
        $user = Auth::user();

        $request->validate([
            'title' => 'required|string|max:150',
            'description' => 'nullable|string',
            'assignee_id' => [
                'required',
                'integer',
                Rule::exists('users', 'id')->where(function ($query) use ($user) {
                    // Can assign to staff or manager (themselves)
                    $query->where('id', $user->id)
                        ->orWhere('manager_id', $user->id);
                })
            ]
        ]);

        $task = Task::create([
            'creator_id' => $user->id,
            'assignee_id' => $request->assignee_id,
            'status_id' => 1, // Default status: To Do
            'title' => $request->title,
            'description' => $request->description,
        ]);

        $task->load(['creator', 'assignee', 'status']);

        return $this->success($task, 'Task created successfully', 201);
    }

    /**
     * @OA\Get(
     *     path="/api/tasks/{id}",
     *     summary="Get a specific task",
     *     description="Fetch a task with creator, assignee, and status relationships",
     *     tags={"Tasks"},
     *     security={{"sanctum": {}}},
     *     @OA\Parameter(
     *         name="id",
     *         in="path",
     *         description="Task ID",
     *         required=true,
     *         @OA\Schema(type="integer")
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Task retrieved successfully",
     *         @OA\JsonContent(
     *             @OA\Property(property="status", type="boolean", example=true),
     *             @OA\Property(property="message", type="string", example="Task retrieved successfully"),
     *             @OA\Property(property="data", type="object")
     *         )
     *     ),
     *     @OA\Response(
     *         response=404,
     *         description="Task not found",
     *         @OA\JsonContent(
     *             @OA\Property(property="status", type="boolean", example=false),
     *             @OA\Property(property="message", type="string", example="Task not found")
     *         )
     *     )
     * )
     */
    public function show($id)
    {
        $task = Task::with(['creator', 'assignee', 'status'])->find($id);

        if (!$task) {
            return $this->failed('Task not found', 404);
        }

        return $this->success($task, 'Task retrieved successfully');
    }

    /**
     * @OA\Put(
     *     path="/api/tasks/{id}",
     *     summary="Update a task",
     *     description="Update a task (only by creator, not allowed when status is 2 or 3)",
     *     tags={"Tasks"},
     *     security={{"sanctum": {}}},
     *     @OA\Parameter(
     *         name="id",
     *         in="path",
     *         description="Task ID",
     *         required=true,
     *         @OA\Schema(type="integer")
     *     ),
     *     @OA\RequestBody(
     *         required=true,
     *         @OA\JsonContent(
     *             @OA\Property(property="title", type="string", maxLength=150, example="Updated Task"),
     *             @OA\Property(property="description", type="string", example="Updated description"),
     *             @OA\Property(property="assignee_id", type="integer", example=2)
     *         )
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Task updated successfully",
     *         @OA\JsonContent(
     *             @OA\Property(property="status", type="boolean", example=true),
     *             @OA\Property(property="message", type="string", example="Task updated successfully"),
     *             @OA\Property(property="data", type="object")
     *         )
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Forbidden - Cannot update task",
     *         @OA\JsonContent(
     *             @OA\Property(property="status", type="boolean", example=false),
     *             @OA\Property(property="message", type="string", example="Cannot update task when status is Doing or Done")
     *         )
     *     )
     * )
     */
    public function update(Request $request, $id)
    {
        $user = Auth::user();
        $task = Task::find($id);

        if (!$task) {
            return $this->failed('Task not found', 404);
        }

        // Only creator can update
        if ($task->creator_id !== $user->id) {
            return $this->failed('Only the creator can update this task', 403);
        }

        // Cannot update when status is 2 (Doing) or 3 (Done)
        if (in_array($task->status_id, [2, 3])) {
            return $this->failed('Cannot update task when status is Doing or Done', 403);
        }

        $request->validate([
            'title' => 'sometimes|required|string|max:150',
            'description' => 'nullable|string',
            'assignee_id' => [
                'sometimes',
                'required',
                'integer',
                Rule::exists('users', 'id')->where(function ($query) use ($user) {
                    $query->where('id', $user->id)
                        ->orWhere('manager_id', $user->id);
                })
            ]
        ]);

        $task->update($request->only(['title', 'description', 'assignee_id']));
        $task->load(['creator', 'assignee', 'status']);

        return $this->success($task, 'Task updated successfully');
    }

    /**
     * @OA\Patch(
     *     path="/api/tasks/{id}/status",
     *     summary="Update task status",
     *     description="Update task status (only by creator, status can only be changed when current status is Doing, Canceled only from Doing)",
     *     tags={"Tasks"},
     *     security={{"sanctum": {}}},
     *     @OA\Parameter(
     *         name="id",
     *         in="path",
     *         description="Task ID",
     *         required=true,
     *         @OA\Schema(type="integer")
     *     ),
     *     @OA\RequestBody(
     *         required=true,
     *         @OA\JsonContent(
     *             required={"status_id"},
     *             @OA\Property(property="status_id", type="integer", example=3, description="3=Done, 4=Canceled (only from Doing status)")
     *         )
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Task status updated successfully",
     *         @OA\JsonContent(
     *             @OA\Property(property="status", type="boolean", example=true),
     *             @OA\Property(property="message", type="string", example="Task status updated successfully"),
     *             @OA\Property(property="data", type="object")
     *         )
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Forbidden - Invalid status transition",
     *         @OA\JsonContent(
     *             @OA\Property(property="status", type="boolean", example=false),
     *             @OA\Property(property="message", type="string", example="Invalid status transition")
     *         )
     *     )
     * )
     */
    public function updateStatus(Request $request, $id)
    {
        $user = Auth::user();
        $task = Task::find($id);

        if (!$task) {
            return $this->failed('Task not found', 404);
        }

        // Only creator can update status
        if ($task->creator_id !== $user->id) {
            return $this->failed('Only the creator can update task status', 403);
        }

        $request->validate([
            'status_id' => 'required|integer|in:3,4'
        ]);

        $currentStatus = $task->status_id;
        $newStatus = $request->status_id;

        // Status can only be changed when current status is "Doing" (2)
        if ($currentStatus != 2) {
            return $this->failed('Status can only be changed when current status is Doing', 403);
        }

        // Valid transitions from Doing (2)
        $validTransitions = [3, 4]; // Doing -> Done, Canceled

        // Check if transition is valid
        if (!in_array($newStatus, $validTransitions)) {
            return $this->failed('Invalid status transition. From Doing, you can only change to Done or Canceled', 403);
        }

        // Special rules for Canceled status
        if ($newStatus == 4) {
            // Cannot cancel if report is filled
            if (!empty($task->report)) {
                return $this->failed('Cannot cancel task when report is filled', 403);
            }
        }

        $task->update(['status_id' => $newStatus]);
        $task->load(['creator', 'assignee', 'status']);

        return $this->success($task, 'Task status updated successfully');
    }

    /**
     * @OA\Patch(
     *     path="/api/tasks/{id}/report",
     *     summary="Update task report",
     *     description="Update task report (only when status is Done, report cannot be empty)",
     *     tags={"Tasks"},
     *     security={{"sanctum": {}}},
     *     @OA\Parameter(
     *         name="id",
     *         in="path",
     *         description="Task ID",
     *         required=true,
     *         @OA\Schema(type="integer")
     *     ),
     *     @OA\RequestBody(
     *         required=true,
     *         @OA\JsonContent(
     *             required={"report"},
     *             @OA\Property(property="report", type="string", example="Task completed successfully with all requirements met.")
     *         )
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Task report updated successfully",
     *         @OA\JsonContent(
     *             @OA\Property(property="status", type="boolean", example=true),
     *             @OA\Property(property="message", type="string", example="Task report updated successfully"),
     *             @OA\Property(property="data", type="object")
     *         )
     *     ),
     *     @OA\Response(
     *         response=404,
     *         description="Task not found",
     *         @OA\JsonContent(
     *             @OA\Property(property="status", type="boolean", example=false),
     *             @OA\Property(property="message", type="string", example="Task not found")
     *         )
     *     )
     * )
     */
    public function updateReport(Request $request, $id)
    {
        $task = Task::find($id);

        if (!$task) {
            return $this->failed('Task not found', 404);
        }

        // Report can only be updated when status is Done (3)
        if ($task->status_id != 3) {
            return $this->failed('Report can only be updated when task status is Done', 403);
        }

        $request->validate([
            'report' => 'required|string|min:1'
        ]);

        $task->update(['report' => $request->report]);
        $task->load(['creator', 'assignee', 'status']);

        return $this->success($task, 'Task report updated successfully');
    }
}
