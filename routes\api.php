<?php

use Illuminate\Support\Facades\Route;

Route::post('/login', [App\Http\Controllers\AuthController::class, 'login']);
Route::post('/logout', [App\Http\Controllers\AuthController::class, 'logout']);

Route::middleware('auth:user')->group(function () {
    Route::get('/profile', [App\Http\Controllers\ProfileController::class, 'get_profile']);
    Route::get('/users', [App\Http\Controllers\UserController::class, 'index']);
    Route::post('/users', [App\Http\Controllers\UserController::class, 'create']);

    // Task Management Routes
    Route::get('/tasks', [App\Http\Controllers\TaskController::class, 'index']);
    Route::post('/tasks', [App\Http\Controllers\TaskController::class, 'store']);
    Route::get('/tasks/{id}', [App\Http\Controllers\TaskController::class, 'show']);
    Route::put('/tasks/{id}', [App\Http\Controllers\TaskController::class, 'update']);
    Route::patch('/tasks/{id}/status', [App\Http\Controllers\TaskController::class, 'updateStatus']);
    Route::patch('/tasks/{id}/report', [App\Http\Controllers\TaskController::class, 'updateReport']);
});
