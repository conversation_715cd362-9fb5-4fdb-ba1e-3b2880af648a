# Git
.git
.gitignore
.gitattributes

# Docker
Dockerfile
docker-compose.yml
.dockerignore

# Documentation
README.md
*.md

# Environment files
.env
.env.local
.env.example

# Node modules and build files
node_modules
npm-debug.log
yarn-error.log

# IDE files
.vscode
.idea
*.swp
*.swo

# OS files
.DS_Store
Thumbs.db

# Laravel specific
/storage/app/*
!/storage/app/.gitignore
/storage/framework/cache/*
!/storage/framework/cache/.gitignore
/storage/framework/sessions/*
!/storage/framework/sessions/.gitignore
/storage/framework/views/*
!/storage/framework/views/.gitignore
/storage/logs/*
!/storage/logs/.gitignore

# Vendor (will be installed during build)
/vendor

# Tests
/tests
phpunit.xml
.phpunit.result.cache

# Build artifacts
/public/hot
/public/storage
/public/build
