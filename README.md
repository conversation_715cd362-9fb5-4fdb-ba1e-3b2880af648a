# IKT Backend API

A Laravel-based REST API for task management system with user authentication and role-based access control.

## Features

-   🔐 **User Authentication** with Laravel Sanctum
-   👥 **User Management** with roles (Manager/Staff)
-   📋 **Task Management** with status workflow
-   📊 **Status Tracking** (To Do → Doing → Done/Canceled)
-   📝 **Task Reports** for completed tasks
-   📚 **API Documentation** with Swagger/OpenAPI
-   🐳 **Docker Support** for easy deployment

## Prerequisites

Before running this application, make sure you have installed:

-   **Docker** and **Docker Compose**
-   **Git** (for cloning the repository)

## Quick Start with Docker

### 1. <PERSON>lone the Repository

```bash
git clone <repository-url>
cd ikt-backend
```

### 2. Build and Run with Docker

```bash
# Build and start all services
docker-compose up -d --build

# View logs (optional)
docker-compose logs -f app
```

### 3. Access the Application

-   **API Base URL**: http://localhost:8000
-   **API Documentation**: http://localhost:8000/api/documentation
-   **MySQL Database**: localhost:3307

### 4. Default Login Credentials

The system automatically seeds a default manager account:

```
Email: <EMAIL>
Password: password
```

## Docker Services

### App Service

-   **Image**: Custom PHP 8.3-CLI
-   **Port**: 8000
-   **Features**: Auto-migration, seeding, and Swagger generation

### MySQL Service

-   **Image**: MySQL 8.0.30
-   **Port**: 3307 (external), 3306 (internal)
-   **Database**: ikt_backend
-   **Username**: ikt
-   **Password**: ********

## API Endpoints

### Authentication

```
POST /api/login     - User login
POST /api/logout    - User logout
```

### Profile

```
GET /api/profile    - Get user profile
```

### Users

```
GET /api/users/     - List all users
POST /api/users/    - Create new user
```

### Tasks

```
GET /api/tasks/              - List tasks (with status filter)
POST /api/tasks/             - Create new task
GET /api/tasks/{id}          - Get specific task
PUT /api/tasks/{id}          - Update task
PATCH /api/tasks/{id}/status - Update task status
PATCH /api/tasks/{id}/report - Update task report
```

## Task Status Workflow

```
To Do (1) → Doing (2) → Done (3) → [Report can be updated]
                   ↘ Canceled (4)

Done (3) → To Do (1) [only if no report]
Canceled (4) → To Do (1)
```

### Status Rules:

-   **To Do → Doing**: Anyone can start a task
-   **Doing → Done/Canceled**: Only from Doing status
-   **Done → To Do**: Only if no report is filled
-   **Canceled → To Do**: Always allowed
-   **Report Updates**: Only when status is Done

## Development Commands

### Docker Management

```bash
# Start services
docker-compose up -d

# Stop services
docker-compose down

# View logs
docker-compose logs -f app

# Rebuild containers
docker-compose up -d --build

# Reset database (removes all data)
docker-compose down -v
```

### Laravel Commands (inside container)

```bash
# Access container shell
docker-compose exec app bash

# Run migrations
php artisan migrate

# Run seeders
php artisan db:seed

# Generate Swagger docs
php artisan l5-swagger:generate

# Clear cache
php artisan cache:clear
php artisan config:clear
```

## Environment Configuration

The application uses `.env.production` for Docker deployment with the following key settings:

```env
APP_ENV=production
APP_DEBUG=false
DB_HOST=mysql
DB_DATABASE=ikt_backend
DB_USERNAME=ikt
DB_PASSWORD=********
```

## API Documentation

Interactive API documentation is available at:
**http://localhost:8000/api/documentation**

The documentation includes:

-   All available endpoints
-   Request/response examples
-   Authentication requirements
-   Status code explanations

## Troubleshooting

### Common Issues

1. **Port already in use**:

    ```bash
    # Change ports in docker-compose.yml
    ports:
      - "8001:8000"  # Change 8000 to 8001
    ```

2. **Database connection failed**:

    ```bash
    # Check if MySQL container is running
    docker-compose ps

    # Restart services
    docker-compose restart
    ```

3. **Permission errors**:
    ```bash
    # Fix storage permissions
    docker-compose exec app chmod -R 755 storage bootstrap/cache
    ```

### Logs and Debugging

```bash
# View application logs
docker-compose logs app

# View MySQL logs
docker-compose logs mysql

# Follow logs in real-time
docker-compose logs -f
```

## Production Deployment

For production deployment:

1. Update `.env.production` with production values
2. Set `APP_DEBUG=false`
3. Configure proper database credentials
4. Set up SSL/HTTPS
5. Use a reverse proxy (nginx/Apache)
6. Set up proper backup strategies

## License

This project is open-sourced software licensed under the [MIT license](https://opensource.org/licenses/MIT).
