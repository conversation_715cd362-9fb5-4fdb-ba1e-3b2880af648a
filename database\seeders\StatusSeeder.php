<?php

namespace Database\Seeders;

use App\Models\Status;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class StatusSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        Status::insert([
            ['id' => 1, 'name' => 'To Do', 'created_at' => now(), 'updated_at' => now()],
            ['id' => 2, 'name' => 'Doing', 'created_at' => now(), 'updated_at' => now()],
            ['id' => 3, 'name' => 'Done', 'created_at' => now(), 'updated_at' => now()],
            ['id' => 4, 'name' => 'Canceled', 'created_at' => now(), 'updated_at' => now()],
        ]);
    }
}
