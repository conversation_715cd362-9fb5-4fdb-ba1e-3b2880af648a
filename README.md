# IKT Backend API - Usage Guide

## Prerequisites

-   **Docker** and **Docker Compose**

## How to Run

### 1. Extract Project

Extract the zip file and navigate to the project directory:

```bash
cd ikt-backend
```

### 2. Create Network

Create the Docker network first:

```bash
docker network create app-network
```

### 3. Build and Run

Build and start the application:

```bash
docker compose up -d --build
```

### 4. Access Application

-   **API**: http://localhost:8000
-   **Documentation**: http://localhost:8000/api/documentation
-   **Database**: localhost:3307

### 5. Default Login

```
Email: <EMAIL>
Password: password
```

## Basic Commands

```bash
# Start services
docker compose up -d

# Stop services
docker compose down

# View logs
docker compose logs -f app

# Rebuild
docker compose up -d --build

# Reset database
docker compose down -v
```

## Database Info

-   **Host**: localhost:3307
-   **Database**: ikt_backend
-   **Username**: ikt
-   **Password**: 12345678
