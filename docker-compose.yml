version: "3.8"

services:
    app:
        build:
            context: .
            dockerfile: Dockerfile
        container_name: ikt-backend-app
        restart: unless-stopped
        ports:
            - "8000:8000"
        volumes:
            - ./storage:/var/www/html/storage
            - ./bootstrap/cache:/var/www/html/bootstrap/cache
        depends_on:
            - mysql
        networks:
            - app-network
        environment:
            - APP_ENV=production
            - DB_HOST=mysql
            - DB_PORT=3306
            - DB_DATABASE=ikt_backend
            - DB_USERNAME=ikt
            - DB_PASSWORD=12345678

    mysql:
        image: mysql:8.0.30
        container_name: ikt-backend-mysql
        restart: unless-stopped
        ports:
            - "3307:3306"
        environment:
            MYSQL_DATABASE: ikt_backend
            MYSQL_USER: ikt
            MYSQL_PASSWORD: 12345678
            MYSQL_ROOT_PASSWORD: rootpassword
        volumes:
            - mysql_data:/var/lib/mysql
        networks:
            - app-network
        command: --default-authentication-plugin=mysql_native_password

volumes:
    mysql_data:
        driver: local

networks:
    app-network:
        driver: bridge
        external: true
