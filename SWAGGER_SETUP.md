# Swagger API Documentation Setup

This project has been integrated with Swagger/OpenAPI documentation using the `darkaonline/l5-swagger` package.

## Accessing the Documentation

Once the Laravel server is running, you can access the Swagger UI at:

```
http://localhost:8000/api/documentation
```

## Available Endpoints

The following API endpoints are documented:

### Authentication

-   **POST** `/api/login` - User login
-   **POST** `/api/logout` - User logout (requires authentication)

### Profile

-   **GET** `/api/profile` - Get user profile (requires authentication)

## Authentication

The API uses Bearer token authentication. To test authenticated endpoints:

1. First, use the `/api/login` endpoint to get an access token
2. Click the "Authorize" button in Swagger UI
3. Enter `Bearer <your-token>` in the authorization field
4. Now you can test protected endpoints

## Generating Documentation

To regenerate the Swagger documentation after making changes to the annotations, you can use either:

```bash
# Using the custom command (recommended)
php artisan swagger:generate

# Or using the package command directly
php artisan l5-swagger:generate
```

The custom `swagger:generate` command provides additional feedback and shows the URL where you can view the documentation.

## Adding New Endpoints

To document new API endpoints:

1. Add OpenAPI annotations to your controller methods
2. Use the `@OA\` attributes for documentation
3. Run `php artisan l5-swagger:generate` to update the documentation

### Example Annotation

```php
/**
 * @OA\Get(
 *     path="/api/example",
 *     summary="Example endpoint",
 *     description="Description of what this endpoint does",
 *     tags={"Example"},
 *     security={{"bearerAuth":{}}},
 *     @OA\Response(
 *         response=200,
 *         description="Success response",
 *         @OA\JsonContent(
 *             @OA\Property(property="status", type="boolean", example=true),
 *             @OA\Property(property="message", type="string", example="Success"),
 *             @OA\Property(property="data", type="object")
 *         )
 *     )
 * )
 */
```

## Configuration

The Swagger configuration is located in `config/l5-swagger.php`. Key settings:

-   **Route**: `/api/documentation` (can be changed in config)
-   **Scan paths**: `app/` directory (includes all controllers)
-   **Output**: JSON format stored in `storage/api-docs/`

## Package Information

-   **Package**: `darkaonline/l5-swagger`
-   **Version**: Latest compatible with Laravel 12
-   **Documentation**: [GitHub Repository](https://github.com/DarkaOnLine/L5-Swagger)
