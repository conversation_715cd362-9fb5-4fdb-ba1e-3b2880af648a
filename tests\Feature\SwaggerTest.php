<?php

namespace Tests\Feature;

use Tests\TestCase;

class SwaggerTest extends TestCase
{
    /**
     * Test that Swagger documentation is accessible.
     */
    public function test_swagger_documentation_is_accessible(): void
    {
        $response = $this->get('/api/documentation');

        $response->assertStatus(200);
    }

    /**
     * Test that Swagger JSON documentation is generated.
     */
    public function test_swagger_json_is_generated(): void
    {
        $response = $this->get('/docs');

        $response->assertStatus(200);
        $response->assertHeader('content-type', 'application/json');

        $json = $response->json();
        $this->assertEquals('3.0.0', $json['openapi']);
        $this->assertEquals('IKT Backend API', $json['info']['title']);
    }

    /**
     * Test that API endpoints are documented in Swagger.
     */
    public function test_api_endpoints_are_documented(): void
    {
        $response = $this->get('/docs');

        $json = $response->json();
        $paths = $json['paths'];

        // Check that our documented endpoints exist
        $this->assertArrayHasKey('/api/login', $paths);
        $this->assertArrayHasKey('/api/logout', $paths);
        $this->assertArrayHasKey('/api/profile', $paths);

        // Check that login endpoint has proper documentation
        $loginEndpoint = $paths['/api/login']['post'];
        $this->assertEquals('User login', $loginEndpoint['summary']);
        $this->assertContains('Authentication', $loginEndpoint['tags']);
    }
}
