#!/bin/bash

# Function to check if command exists (cross-platform)
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Function to wait for MySQL (cross-platform)
wait_for_mysql() {
    echo "Waiting for MySQL to be ready..."

    # Try different netcat commands based on availability
    if command_exists nc; then
        while ! nc -z mysql 3306; do
            sleep 1
        done
    elif command_exists netcat; then
        while ! netcat -z mysql 3306; do
            sleep 1
        done
    else
        # Fallback: try to connect with PHP
        while ! php -r "
            \$connection = @mysqli_connect('mysql', 'ikt', '12345678', 'ikt_backend');
            if (!\$connection) {
                exit(1);
            }
            mysqli_close(\$connection);
            exit(0);
        "; do
            echo "Waiting for MySQL connection..."
            sleep 2
        done
    fi

    echo "MySQL is ready!"
}

# Wait for MySQL to be ready
wait_for_mysql

# Generate application key if not set
if [ "$APP_KEY" = "base64:YOUR_APP_KEY_HERE" ] || [ -z "$APP_KEY" ]; then
    echo "Generating application key..."
    php artisan key:generate --force
fi

# Run database migrations
echo "Running database migrations..."
php artisan migrate --force

# Run database seeders
echo "Running database seeders..."
php artisan db:seed --force

# Generate Swagger documentation
echo "Generating Swagger documentation..."
php artisan l5-swagger:generate

# Clear and cache config for production
echo "Optimizing Laravel..."
php artisan config:cache
php artisan route:cache
php artisan view:cache

# Start PHP built-in server
echo "Starting PHP development server on 0.0.0.0:8000..."
exec php artisan serve --host=0.0.0.0 --port=8000
