#!/bin/bash

# Wait for MySQL to be ready
echo "Waiting for MySQL to be ready..."
while ! nc -z mysql 3306; do
  sleep 1
done
echo "MySQL is ready!"

# Generate application key if not set
if [ "$APP_KEY" = "base64:YOUR_APP_KEY_HERE" ] || [ -z "$APP_KEY" ]; then
    echo "Generating application key..."
    php artisan key:generate --force
fi

# Check if database is fresh (no migrations table exists)
echo "Checking database status..."
DB_FRESH=$(php -r "
try {
    \$pdo = new PDO('mysql:host=mysql;dbname=ikt_backend', 'ikt', '12345678');
    \$stmt = \$pdo->query('SHOW TABLES LIKE \"migrations\"');
    echo \$stmt->rowCount() == 0 ? 'true' : 'false';
} catch (Exception \$e) {
    echo 'true';
}
")

if [ "$DB_FRESH" = "true" ]; then
    echo "Fresh database detected. Running migrations and seeders..."

    # Run database migrations
    echo "Running database migrations..."
    php artisan migrate

    # Run database seeders
    echo "Running database seeders..."
    php artisan db:seed
else
    echo "Database already initialized. Skipping migrations and seeders."
fi

# Generate Swagger documentation
echo "Generating Swagger documentation..."
php artisan l5-swagger:generate

# Clear and cache config
echo "Optimizing Laravel..."
php artisan config:cache
php artisan route:cache
php artisan view:cache

# Start PHP built-in server
echo "Starting PHP development server on 0.0.0.0:8000..."
exec php artisan serve --host=0.0.0.0 --port=8000
