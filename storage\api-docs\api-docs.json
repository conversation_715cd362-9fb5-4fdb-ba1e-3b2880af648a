{"openapi": "3.0.0", "info": {"title": "IKT Backend API", "description": "API documentation for IKT Backend application", "contact": {"email": "<EMAIL>"}, "version": "1.0.0"}, "servers": [{"url": "http://localhost:8000", "description": "API Server"}], "paths": {"/api/login": {"post": {"tags": ["Authentication"], "summary": "User login", "description": "Authenticate user and return access token", "operationId": "a3b306d14572d1f4bd6c064b3233e7b8", "requestBody": {"required": true, "content": {"application/json": {"schema": {"required": ["email", "password"], "properties": {"email": {"type": "string", "format": "email", "example": "<EMAIL>"}, "password": {"type": "string", "format": "password", "example": "password123"}}, "type": "object"}}}}, "responses": {"200": {"description": "Login successful", "content": {"application/json": {"schema": {"properties": {"status": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "<PERSON><PERSON>"}, "data": {"type": "string", "example": "1|abcdef123456..."}}, "type": "object"}}}}, "422": {"description": "Validation error", "content": {"application/json": {"schema": {"properties": {"status": {"type": "boolean", "example": false}, "message": {"type": "string", "example": "E-mail does not exists"}}, "type": "object"}}}}}}}, "/api/logout": {"post": {"tags": ["Authentication"], "summary": "User logout", "description": "Logout user and revoke access token", "operationId": "fe8f3429cd6979b3b4517e186505f9f9", "responses": {"200": {"description": "Logout successful", "content": {"application/json": {"schema": {"properties": {"status": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "Logout Succeed"}}, "type": "object"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"properties": {"status": {"type": "boolean", "example": false}, "message": {"type": "string", "example": "Unauthorized"}}, "type": "object"}}}}}, "security": [{"bearerAuth": []}]}}, "/api/profile": {"get": {"tags": ["Profile"], "summary": "Get user profile", "description": "Get authenticated user's profile information", "operationId": "83a4faceaeb0b4c773389d2955bfe424", "responses": {"200": {"description": "Profile retrieved successfully", "content": {"application/json": {"schema": {"properties": {"status": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "Get Profile"}, "data": {"properties": {"id": {"type": "integer", "example": 1}, "name": {"type": "string", "example": "<PERSON>"}, "email": {"type": "string", "example": "<EMAIL>"}, "created_at": {"type": "string", "format": "date-time"}, "updated_at": {"type": "string", "format": "date-time"}}, "type": "object"}}, "type": "object"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"properties": {"status": {"type": "boolean", "example": false}, "message": {"type": "string", "example": "Unauthorized"}}, "type": "object"}}}}}, "security": [{"bearerAuth": []}]}}, "/api/tasks": {"get": {"tags": ["Tasks"], "summary": "Get tasks", "description": "Get tasks filtered by status and ordered by latest", "operationId": "471d938a3bfaeb6fbd981d7c3e2728b0", "parameters": [{"name": "status", "in": "query", "description": "Filter by status ID", "required": false, "schema": {"type": "integer", "example": 1}}], "responses": {"200": {"description": "Tasks retrieved successfully", "content": {"application/json": {"schema": {"properties": {"status": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "Tasks retrieved successfully"}, "data": {"type": "array", "items": {"properties": {"id": {"type": "integer", "example": 1}, "title": {"type": "string", "example": "Task Title"}, "description": {"type": "string", "example": "Task description"}, "report": {"type": "string", "example": "Task report", "nullable": true}, "creator_id": {"type": "integer", "example": 1}, "assignee_id": {"type": "integer", "example": 2}, "status_id": {"type": "integer", "example": 1}, "created_at": {"type": "string", "format": "date-time"}, "updated_at": {"type": "string", "format": "date-time"}}, "type": "object"}}}, "type": "object"}}}}}, "security": [{"sanctum": []}]}, "post": {"tags": ["Tasks"], "summary": "Create a new task", "description": "Create a new task with creator as logged in user and default status 1 (To Do)", "operationId": "f803ebdb2300acdb7e93bbe8d0b9a2e8", "requestBody": {"required": true, "content": {"application/json": {"schema": {"required": ["title", "assignee_id"], "properties": {"title": {"type": "string", "maxLength": 150, "example": "New Task"}, "description": {"type": "string", "example": "Task description"}, "assignee_id": {"type": "integer", "example": 2}}, "type": "object"}}}}, "responses": {"201": {"description": "Task created successfully", "content": {"application/json": {"schema": {"properties": {"status": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "Task created successfully"}, "data": {"type": "object"}}, "type": "object"}}}}, "422": {"description": "Validation error", "content": {"application/json": {"schema": {"properties": {"status": {"type": "boolean", "example": false}, "message": {"type": "string", "example": "Validation failed"}}, "type": "object"}}}}}, "security": [{"sanctum": []}]}}, "/api/tasks/{id}": {"get": {"tags": ["Tasks"], "summary": "Get a specific task", "description": "Fetch a task with creator, assignee, and status relationships", "operationId": "90b3cd4d2d4b416dc989d2055dbed06c", "parameters": [{"name": "id", "in": "path", "description": "Task ID", "required": true, "schema": {"type": "integer"}}], "responses": {"200": {"description": "Task retrieved successfully", "content": {"application/json": {"schema": {"properties": {"status": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "Task retrieved successfully"}, "data": {"type": "object"}}, "type": "object"}}}}, "404": {"description": "Task not found", "content": {"application/json": {"schema": {"properties": {"status": {"type": "boolean", "example": false}, "message": {"type": "string", "example": "Task not found"}}, "type": "object"}}}}}, "security": [{"sanctum": []}]}, "put": {"tags": ["Tasks"], "summary": "Update a task", "description": "Update a task (only by creator, not allowed when status is 2 or 3)", "operationId": "1aaf37e74360ef4c10fadaee02ae77fb", "parameters": [{"name": "id", "in": "path", "description": "Task ID", "required": true, "schema": {"type": "integer"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"properties": {"title": {"type": "string", "maxLength": 150, "example": "Updated Task"}, "description": {"type": "string", "example": "Updated description"}, "assignee_id": {"type": "integer", "example": 2}}, "type": "object"}}}}, "responses": {"200": {"description": "Task updated successfully", "content": {"application/json": {"schema": {"properties": {"status": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "Task updated successfully"}, "data": {"type": "object"}}, "type": "object"}}}}, "403": {"description": "Forbidden - Cannot update task", "content": {"application/json": {"schema": {"properties": {"status": {"type": "boolean", "example": false}, "message": {"type": "string", "example": "Cannot update task when status is Doing or Done"}}, "type": "object"}}}}}, "security": [{"sanctum": []}]}}, "/api/tasks/{id}/status": {"patch": {"tags": ["Tasks"], "summary": "Update task status", "description": "Update task status (only by creator, status can only be changed when current status is Doing, Canceled only from Doing)", "operationId": "d67da4a89702cb87fd0b03e593801562", "parameters": [{"name": "id", "in": "path", "description": "Task ID", "required": true, "schema": {"type": "integer"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"required": ["status_id"], "properties": {"status_id": {"description": "3=Done, 4=Canceled (only from Doing status)", "type": "integer", "example": 3}}, "type": "object"}}}}, "responses": {"200": {"description": "Task status updated successfully", "content": {"application/json": {"schema": {"properties": {"status": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "Task status updated successfully"}, "data": {"type": "object"}}, "type": "object"}}}}, "403": {"description": "Forbidden - Invalid status transition", "content": {"application/json": {"schema": {"properties": {"status": {"type": "boolean", "example": false}, "message": {"type": "string", "example": "Invalid status transition"}}, "type": "object"}}}}}, "security": [{"sanctum": []}]}}, "/api/tasks/{id}/report": {"patch": {"tags": ["Tasks"], "summary": "Update task report", "description": "Update task report (can be done by anyone)", "operationId": "5a984d5138d978c136139a276341d8f0", "parameters": [{"name": "id", "in": "path", "description": "Task ID", "required": true, "schema": {"type": "integer"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"required": ["report"], "properties": {"report": {"type": "string", "example": "Task completed successfully with all requirements met."}}, "type": "object"}}}}, "responses": {"200": {"description": "Task report updated successfully", "content": {"application/json": {"schema": {"properties": {"status": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "Task report updated successfully"}, "data": {"type": "object"}}, "type": "object"}}}}, "404": {"description": "Task not found", "content": {"application/json": {"schema": {"properties": {"status": {"type": "boolean", "example": false}, "message": {"type": "string", "example": "Task not found"}}, "type": "object"}}}}}, "security": [{"sanctum": []}]}}, "/api/users": {"get": {"tags": ["Users"], "summary": "Get all users", "description": "Retrieve a paginated list of all users with their roles", "operationId": "c457726701591d1183b53aa71fc13441", "parameters": [{"name": "page", "in": "query", "description": "Page number", "schema": {"type": "integer", "example": 1}}, {"name": "per_page", "in": "query", "description": "Number of items per page", "schema": {"type": "integer", "example": 15}}], "responses": {"200": {"description": "Users retrieved successfully", "content": {"application/json": {"schema": {"properties": {"status": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "Users retrieved successfully"}, "data": {"properties": {"current_page": {"type": "integer", "example": 1}, "data": {"type": "array", "items": {"properties": {"id": {"type": "integer", "example": 1}, "name": {"type": "string", "example": "<PERSON>"}, "email": {"type": "string", "example": "<EMAIL>"}, "role_id": {"type": "integer", "example": 1}, "role": {"properties": {"id": {"type": "integer", "example": 1}, "name": {"type": "string", "example": "admin"}}, "type": "object"}, "created_at": {"type": "string", "format": "date-time"}, "updated_at": {"type": "string", "format": "date-time"}}, "type": "object"}}, "first_page_url": {"type": "string", "example": "http://localhost:8000/api/users?page=1"}, "from": {"type": "integer", "example": 1}, "last_page": {"type": "integer", "example": 1}, "last_page_url": {"type": "string", "example": "http://localhost:8000/api/users?page=1"}, "next_page_url": {"type": "string", "example": "null"}, "path": {"type": "string", "example": "http://localhost:8000/api/users"}, "per_page": {"type": "integer", "example": 15}, "prev_page_url": {"type": "string", "example": "null"}, "to": {"type": "integer", "example": 1}, "total": {"type": "integer", "example": 1}}, "type": "object"}}, "type": "object"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"properties": {"status": {"type": "boolean", "example": false}, "message": {"type": "string", "example": "Unauthorized"}}, "type": "object"}}}}}, "security": [{"bearerAuth": []}]}, "post": {"tags": ["Users"], "summary": "Create a new user", "description": "Create a new user with specified role", "operationId": "1a63883edb751cb640a2f6f516236f49", "requestBody": {"required": true, "content": {"application/json": {"schema": {"required": ["name", "email", "password", "role_id"], "properties": {"name": {"type": "string", "example": "<PERSON>"}, "email": {"type": "string", "format": "email", "example": "<EMAIL>"}, "password": {"type": "string", "format": "password", "example": "password123"}, "role_id": {"type": "integer", "example": 1}}, "type": "object"}}}}, "responses": {"201": {"description": "User created successfully", "content": {"application/json": {"schema": {"properties": {"status": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "User created successfully"}, "data": {"properties": {"id": {"type": "integer", "example": 1}, "name": {"type": "string", "example": "<PERSON>"}, "email": {"type": "string", "example": "<EMAIL>"}, "role_id": {"type": "integer", "example": 1}, "created_at": {"type": "string", "format": "date-time"}, "updated_at": {"type": "string", "format": "date-time"}}, "type": "object"}}, "type": "object"}}}}, "422": {"description": "Validation error", "content": {"application/json": {"schema": {"properties": {"status": {"type": "boolean", "example": false}, "message": {"type": "string", "example": "The given data was invalid."}}, "type": "object"}}}}}, "security": [{"bearerAuth": []}]}}}, "components": {"securitySchemes": {"sanctum": {"type": "http", "bearerFormat": "JWT", "scheme": "bearer"}}}, "tags": [{"name": "Authentication", "description": "Authentication"}, {"name": "Profile", "description": "Profile"}, {"name": "Tasks", "description": "Tasks"}, {"name": "Users", "description": "Users"}]}