<?php

namespace App\Http\Controllers;

use App\Traits\HasJsonResponse;
use OpenApi\Attributes as OA;

/**
 * @OA\Info(
 *     title="IKT Backend API",
 *     version="1.0.0",
 *     description="API documentation for IKT Backend application",
 *     @OA\Contact(
 *         email="<EMAIL>"
 *     )
 * )
 * @OA\Server(
 *     url="http://localhost:8000",
 *     description="API Server"
 * )
 * @OA\SecurityScheme(
 *     securityScheme="bearerAuth",
 *     type="http",
 *     scheme="bearer",
 *     bearerFormat="JWT"
 * )
 */
abstract class Controller
{
    use HasJsonResponse;
}
